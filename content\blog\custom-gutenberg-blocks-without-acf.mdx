---
title: "Custom Gutenberg Blocks: Beyond ACF Pro"
excerpt: "Build native WordPress blocks with PHP and JavaScript without relying on ACF Pro. Learn the WordPress way of block development."
date: "2024-01-05"
category: "WordPress Development"
readTime: "10 min read"
author: "Navhaus Team"
published: false
featured: false
tags: ["gutenberg", "wordpress blocks", "custom blocks", "wordpress development", "block editor", "javascript", "php", "native development", "block api"]
seo:
  title: "Navhaus | Custom Gutenberg Blocks Without ACF Pro"
  description: "Learn to build custom Gutenberg blocks using native WordPress APIs instead of ACF Pro. Complete guide to block development with PHP and JavaScript."
  keywords: ["gutenberg blocks", "wordpress blocks", "custom blocks", "wordpress development", "block editor", "native blocks", "block api", "javascript blocks"]
  canonicalUrl: "https://navhaus.com/blog/custom-gutenberg-blocks-without-acf"
  ogImage: "https://navhaus.com/images/blog/gutenberg-blocks-og.jpg"
  ogImageAlt: "Custom Gutenberg Blocks Without ACF Pro - Native WordPress development"
  twitterImage: "https://navhaus.com/images/blog/gutenberg-blocks-og.jpg"
  twitterImageAlt: "Custom Gutenberg Blocks Guide - Build native WordPress blocks"
  twitterCard: "summary_large_image"
---

While ACF Pro makes creating custom Gutenberg blocks easier, building native blocks gives you more control, better performance, and deeper integration with WordPress. In this guide, we'll explore how to create custom blocks using WordPress's native block APIs.

## Why Build Native Blocks?

Native Gutenberg blocks offer several advantages:

- **Better performance** - No plugin dependencies
- **More control** - Full access to block APIs
- **Future-proof** - Built on WordPress standards
- **Better integration** - Seamless editor experience
- **Cost-effective** - No ACF Pro license required

## Block Development Basics

Every Gutenberg block consists of two main parts:

1. **JavaScript** - Handles the editor interface
2. **PHP** - Handles server-side rendering and registration

## Setting Up Your Development Environment

First, create a basic plugin structure:

```
my-custom-blocks/
├── my-custom-blocks.php
├── src/
│   ├── blocks/
│   │   └── testimonial/
│   │       ├── index.js
│   │       ├── edit.js
│   │       ├── save.js
│   │       └── style.scss
│   └── index.js
├── build/
└── package.json
```

## Creating Your First Block

Let's create a testimonial block. Start with the main plugin file:

```php
<?php
// my-custom-blocks.php

function my_custom_blocks_init() {
    register_block_type( __DIR__ . '/build/blocks/testimonial' );
}
add_action( 'init', 'my_custom_blocks_init' );

function my_custom_blocks_enqueue_editor_assets() {
    wp_enqueue_script(
        'my-custom-blocks-editor',
        plugin_dir_url( __FILE__ ) . 'build/index.js',
        array( 'wp-blocks', 'wp-element', 'wp-editor' ),
        filemtime( plugin_dir_path( __FILE__ ) . 'build/index.js' )
    );
}
add_action( 'enqueue_block_editor_assets', 'my_custom_blocks_enqueue_editor_assets' );
```

## Block Registration (JavaScript)

Create the main block registration file:

```javascript
// src/blocks/testimonial/index.js
import { registerBlockType } from '@wordpress/blocks';
import edit from './edit';
import save from './save';

registerBlockType('my-custom-blocks/testimonial', {
    title: 'Testimonial',
    description: 'Display a customer testimonial with photo and details.',
    category: 'common',
    icon: 'format-quote',
    supports: {
        html: false,
    },
    attributes: {
        content: {
            type: 'string',
            source: 'html',
            selector: '.testimonial-content',
        },
        author: {
            type: 'string',
            source: 'html',
            selector: '.testimonial-author',
        },
        role: {
            type: 'string',
            source: 'html',
            selector: '.testimonial-role',
        },
        imageUrl: {
            type: 'string',
            source: 'attribute',
            selector: '.testimonial-image img',
            attribute: 'src',
        },
        imageAlt: {
            type: 'string',
            source: 'attribute',
            selector: '.testimonial-image img',
            attribute: 'alt',
        },
    },
    edit,
    save,
});
```

## Editor Interface (Edit Component)

Create the editor interface:

```javascript
// src/blocks/testimonial/edit.js
import { __ } from '@wordpress/i18n';
import {
    RichText,
    MediaUpload,
    MediaUploadCheck,
    InspectorControls,
} from '@wordpress/block-editor';
import {
    PanelBody,
    Button,
    ResponsiveWrapper,
} from '@wordpress/components';

export default function Edit({ attributes, setAttributes }) {
    const { content, author, role, imageUrl, imageAlt } = attributes;

    const onSelectImage = (media) => {
        setAttributes({
            imageUrl: media.url,
            imageAlt: media.alt,
        });
    };

    const removeImage = () => {
        setAttributes({
            imageUrl: undefined,
            imageAlt: undefined,
        });
    };

    return (
        <>
            <InspectorControls>
                <PanelBody title={__('Image Settings', 'my-custom-blocks')}>
                    <MediaUploadCheck>
                        <MediaUpload
                            onSelect={onSelectImage}
                            allowedTypes={['image']}
                            value={imageUrl}
                            render={({ open }) => (
                                <Button
                                    className={imageUrl ? 'editor-post-featured-image__preview' : 'editor-post-featured-image__toggle'}
                                    onClick={open}
                                >
                                    {imageUrl ? __('Replace Image', 'my-custom-blocks') : __('Select Image', 'my-custom-blocks')}
                                </Button>
                            )}
                        />
                    </MediaUploadCheck>
                    {imageUrl && (
                        <Button onClick={removeImage} isDestructive>
                            {__('Remove Image', 'my-custom-blocks')}
                        </Button>
                    )}
                </PanelBody>
            </InspectorControls>

            <div className="testimonial-block">
                {imageUrl && (
                    <div className="testimonial-image">
                        <ResponsiveWrapper
                            naturalWidth={150}
                            naturalHeight={150}
                        >
                            <img src={imageUrl} alt={imageAlt} />
                        </ResponsiveWrapper>
                    </div>
                )}

                <div className="testimonial-content-wrapper">
                    <RichText
                        tagName="blockquote"
                        className="testimonial-content"
                        value={content}
                        onChange={(value) => setAttributes({ content: value })}
                        placeholder={__('Enter testimonial content...', 'my-custom-blocks')}
                    />

                    <RichText
                        tagName="cite"
                        className="testimonial-author"
                        value={author}
                        onChange={(value) => setAttributes({ author: value })}
                        placeholder={__('Author name', 'my-custom-blocks')}
                    />

                    <RichText
                        tagName="span"
                        className="testimonial-role"
                        value={role}
                        onChange={(value) => setAttributes({ role: value })}
                        placeholder={__('Author role/company', 'my-custom-blocks')}
                    />
                </div>
            </div>
        </>
    );
}
```

## Frontend Output (Save Component)

Define how the block appears on the frontend:

```javascript
// src/blocks/testimonial/save.js
import { RichText } from '@wordpress/block-editor';

export default function Save({ attributes }) {
    const { content, author, role, imageUrl, imageAlt } = attributes;

    return (
        <div className="testimonial-block">
            {imageUrl && (
                <div className="testimonial-image">
                    <img src={imageUrl} alt={imageAlt} />
                </div>
            )}

            <div className="testimonial-content-wrapper">
                <RichText.Content
                    tagName="blockquote"
                    className="testimonial-content"
                    value={content}
                />

                <footer className="testimonial-footer">
                    <RichText.Content
                        tagName="cite"
                        className="testimonial-author"
                        value={author}
                    />
                    {role && (
                        <RichText.Content
                            tagName="span"
                            className="testimonial-role"
                            value={role}
                        />
                    )}
                </footer>
            </div>
        </div>
    );
}
```

## Server-Side Rendering

For dynamic blocks, you can use server-side rendering:

```php
// Alternative approach using render_callback
function register_testimonial_block() {
    register_block_type('my-custom-blocks/testimonial', array(
        'attributes' => array(
            'content' => array(
                'type' => 'string',
                'default' => '',
            ),
            'author' => array(
                'type' => 'string',
                'default' => '',
            ),
            'role' => array(
                'type' => 'string',
                'default' => '',
            ),
        ),
        'render_callback' => 'render_testimonial_block',
    ));
}

function render_testimonial_block($attributes) {
    $content = $attributes['content'] ?? '';
    $author = $attributes['author'] ?? '';
    $role = $attributes['role'] ?? '';

    ob_start();
    ?>
    <div class="testimonial-block">
        <blockquote class="testimonial-content">
            <?php echo wp_kses_post($content); ?>
        </blockquote>
        <footer class="testimonial-footer">
            <cite class="testimonial-author"><?php echo esc_html($author); ?></cite>
            <?php if ($role): ?>
                <span class="testimonial-role"><?php echo esc_html($role); ?></span>
            <?php endif; ?>
        </footer>
    </div>
    <?php
    return ob_get_clean();
}
```

## Advanced Block Features

### Block Variations

Create variations of your block:

```javascript
import { registerBlockVariation } from '@wordpress/blocks';

registerBlockVariation('my-custom-blocks/testimonial', {
    name: 'testimonial-featured',
    title: 'Featured Testimonial',
    description: 'A highlighted testimonial with special styling.',
    attributes: {
        className: 'is-style-featured',
    },
    isDefault: false,
});
```

### Block Styles

Add custom styles:

```javascript
import { registerBlockStyle } from '@wordpress/blocks';

registerBlockStyle('my-custom-blocks/testimonial', {
    name: 'rounded',
    label: 'Rounded',
});

registerBlockStyle('my-custom-blocks/testimonial', {
    name: 'minimal',
    label: 'Minimal',
});
```

### Inner Blocks

Create blocks that contain other blocks:

```javascript
import { InnerBlocks } from '@wordpress/block-editor';

const ALLOWED_BLOCKS = ['core/heading', 'core/paragraph', 'core/image'];

export default function Edit() {
    return (
        <div className="custom-container">
            <InnerBlocks allowedBlocks={ALLOWED_BLOCKS} />
        </div>
    );
}
```

## Build Process

Set up a build process using @wordpress/scripts:

```json
{
  "scripts": {
    "build": "wp-scripts build",
    "start": "wp-scripts start"
  },
  "devDependencies": {
    "@wordpress/scripts": "^24.0.0"
  }
}
```

## Best Practices

1. **Use semantic HTML** - Ensure accessibility
2. **Validate attributes** - Sanitize and validate all inputs
3. **Handle deprecations** - Plan for block structure changes
4. **Test thoroughly** - Test in different themes and contexts
5. **Follow WordPress coding standards** - Use proper hooks and filters

## Conclusion

Building native Gutenberg blocks gives you complete control over functionality and performance. While it requires more initial setup than ACF Pro, the benefits of native development make it worthwhile for serious WordPress projects.

Start with simple blocks and gradually add complexity as you become more comfortable with the block APIs. The WordPress block editor is the future of content creation, and native block development ensures your projects are built on solid foundations.
